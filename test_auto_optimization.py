#!/usr/bin/env python3
"""
测试自动性能优化功能
验证第一次打开时自动运行性能优化
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_auto_optimization_flag():
    """测试自动优化标志功能"""
    print("🔧 测试自动优化标志功能...")
    
    try:
        # 读取配置文件
        config_file = "config/video_composer_config.json"
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查性能优化标志
            performance_optimized = config.get('performance_optimized', False)
            print(f"当前性能优化状态: {performance_optimized}")
            
            # 模拟第一次使用（重置标志）
            config['performance_optimized'] = False
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print("✅ 已重置性能优化标志，模拟第一次使用")
            return True
        else:
            print("❌ 配置文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_config_loading():
    """测试配置加载逻辑"""
    print("\n📁 测试配置加载逻辑...")
    
    try:
        # 导入视频合成模块
        import tkinter as tk
        from modules.video_composer.main_window import VideoComposerWindow
        
        # 创建临时窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建视频合成窗口实例
        window = VideoComposerWindow(root)
        
        # 检查是否有自动优化方法
        if hasattr(window, 'auto_performance_optimization'):
            print("✅ 自动性能优化方法存在")
        else:
            print("❌ 自动性能优化方法不存在")
            return False
        
        # 检查性能优化标志
        if hasattr(window, '_performance_optimized'):
            print(f"✅ 性能优化标志: {window._performance_optimized}")
        else:
            print("⚠️ 性能优化标志未初始化")
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_optimization_application():
    """测试优化设置应用"""
    print("\n⚙️ 测试优化设置应用...")
    
    try:
        # 导入FFmpeg合成器
        from modules.video_composer.ffmpeg_composer import FFmpegVideoComposer
        
        # 创建合成器实例
        composer = FFmpegVideoComposer()
        
        # 运行性能基准测试
        print("运行性能基准测试...")
        results = composer.run_performance_benchmark(
            progress_callback=lambda msg: print(f"  {msg}")
        )
        
        if results:
            print("✅ 性能测试完成")
            
            # 检查结果结构
            if results.get('fastest_encoder'):
                fastest = results['fastest_encoder']
                print(f"最快编码器: {fastest['name']} ({fastest['fps']:.1f} FPS)")
            
            if results.get('optimization_tips'):
                print("优化建议:")
                for tip in results['optimization_tips']:
                    print(f"  • {tip}")
            
            return True
        else:
            print("❌ 性能测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_config_persistence():
    """测试配置持久化"""
    print("\n💾 测试配置持久化...")
    
    try:
        config_file = "config/video_composer_config.json"
        
        # 读取当前配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 备份原始值
        original_optimized = config.get('performance_optimized', False)
        
        # 测试设置为已优化
        config['performance_optimized'] = True
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        # 重新读取验证
        with open(config_file, 'r', encoding='utf-8') as f:
            new_config = json.load(f)
        
        if new_config.get('performance_optimized') == True:
            print("✅ 配置保存成功")
        else:
            print("❌ 配置保存失败")
            return False
        
        # 恢复原始值
        config['performance_optimized'] = original_optimized
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print("✅ 配置已恢复")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 自动性能优化功能测试")
    print("=" * 50)
    
    tests = [
        ("自动优化标志", test_auto_optimization_flag),
        ("配置加载逻辑", test_config_loading),
        ("优化设置应用", test_optimization_application),
        ("配置持久化", test_config_persistence),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！自动性能优化功能正常工作")
        print("\n✨ 功能特点:")
        print("   • 第一次打开时自动运行性能优化")
        print("   • 静默应用最佳设置，无需用户干预")
        print("   • 优化完成后设置标志，避免重复优化")
        print("   • 在状态栏显示优化结果")
        print("   • 移除了手动优化按钮，简化界面")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
