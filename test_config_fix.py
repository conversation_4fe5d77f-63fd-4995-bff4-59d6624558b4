#!/usr/bin/env python3
"""
测试配置保存和读取修复
验证分步编码器设置能正确保存和加载
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config_save_load():
    """测试配置保存和加载"""
    print("🔧 测试配置保存和加载...")
    
    try:
        # 导入视频合成模块
        import tkinter as tk
        from modules.video_composer.main_window import VideoComposerWindow
        
        # 创建临时窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建视频合成窗口实例
        window = VideoComposerWindow(root)
        
        # 设置测试值
        test_subtitle_encoder = "h264_nvenc"
        test_audio_merge_encoder = "h264_qsv"
        test_final_encoder = "h264_amf"
        
        print(f"设置测试值:")
        print(f"  字幕编码器: {test_subtitle_encoder}")
        print(f"  音频合并编码器: {test_audio_merge_encoder}")
        print(f"  最终编码器: {test_final_encoder}")
        
        # 设置编码器
        window.subtitle_encoder_var.set(test_subtitle_encoder)
        window.audio_merge_encoder_var.set(test_audio_merge_encoder)
        window.final_encoder_var.set(test_final_encoder)
        
        # 保存配置
        window.save_video_composer_config()
        print("✅ 配置已保存")
        
        # 重新创建窗口实例来测试加载
        root2 = tk.Tk()
        root2.withdraw()
        window2 = VideoComposerWindow(root2)
        
        # 检查加载的值
        loaded_subtitle = window2.subtitle_encoder_var.get()
        loaded_audio_merge = window2.audio_merge_encoder_var.get()
        loaded_final = window2.final_encoder_var.get()
        
        print(f"加载的值:")
        print(f"  字幕编码器: {loaded_subtitle}")
        print(f"  音频合并编码器: {loaded_audio_merge}")
        print(f"  最终编码器: {loaded_final}")
        
        # 验证
        success = True
        if loaded_subtitle != test_subtitle_encoder:
            print(f"❌ 字幕编码器不匹配: 期望 {test_subtitle_encoder}, 实际 {loaded_subtitle}")
            success = False
        
        if loaded_audio_merge != test_audio_merge_encoder:
            print(f"❌ 音频合并编码器不匹配: 期望 {test_audio_merge_encoder}, 实际 {loaded_audio_merge}")
            success = False
        
        if loaded_final != test_final_encoder:
            print(f"❌ 最终编码器不匹配: 期望 {test_final_encoder}, 实际 {loaded_final}")
            success = False
        
        if success:
            print("✅ 所有编码器设置正确保存和加载")
        
        # 清理
        root.destroy()
        root2.destroy()
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_config_file_content():
    """测试配置文件内容"""
    print("\n📁 测试配置文件内容...")
    
    try:
        config_file = "config/video_composer_config.json"
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查关键配置项
            required_keys = [
                'subtitle_encoder',
                'audio_merge_encoder', 
                'final_encoder',
                'speed_priority',
                'performance_optimized'
            ]
            
            missing_keys = []
            for key in required_keys:
                if key not in config:
                    missing_keys.append(key)
                else:
                    print(f"✅ {key}: {config[key]}")
            
            if missing_keys:
                print(f"❌ 缺少配置项: {missing_keys}")
                return False
            else:
                print("✅ 所有必需的配置项都存在")
                return True
        else:
            print("❌ 配置文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_auto_save_callback():
    """测试自动保存回调"""
    print("\n🔄 测试自动保存回调...")
    
    try:
        import tkinter as tk
        from modules.video_composer.main_window import VideoComposerWindow
        
        # 创建窗口
        root = tk.Tk()
        root.withdraw()
        window = VideoComposerWindow(root)
        
        # 记录初始时间戳
        config_file = "config/video_composer_config.json"
        if os.path.exists(config_file):
            initial_mtime = os.path.getmtime(config_file)
        else:
            initial_mtime = 0
        
        # 触发设置变更
        window.subtitle_encoder_var.set("h264_nvenc")
        window.on_video_setting_changed()
        
        # 等待一下让保存完成
        time.sleep(0.5)
        
        # 检查文件是否更新
        if os.path.exists(config_file):
            new_mtime = os.path.getmtime(config_file)
            if new_mtime > initial_mtime:
                print("✅ 配置文件已自动更新")
                success = True
            else:
                print("❌ 配置文件未更新")
                success = False
        else:
            print("❌ 配置文件不存在")
            success = False
        
        # 清理
        root.destroy()
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_one_click_optimization():
    """测试一键优化功能"""
    print("\n🚀 测试一键优化功能...")
    
    try:
        import tkinter as tk
        from modules.video_composer.main_window import VideoComposerWindow
        
        # 创建窗口
        root = tk.Tk()
        root.withdraw()
        window = VideoComposerWindow(root)
        
        # 运行一键优化
        print("运行一键优化...")
        window.optimize_encoder_settings()
        
        # 检查优化结果
        subtitle_encoder = window.subtitle_encoder_var.get()
        audio_merge_encoder = window.audio_merge_encoder_var.get()
        final_encoder = window.final_encoder_var.get()
        speed_priority = window.speed_priority_var.get()
        
        print(f"优化后的设置:")
        print(f"  字幕编码器: {subtitle_encoder}")
        print(f"  音频合并编码器: {audio_merge_encoder}")
        print(f"  最终编码器: {final_encoder}")
        print(f"  速度优先: {speed_priority}")
        
        # 验证优化是否生效
        if subtitle_encoder != "auto" and audio_merge_encoder != "auto" and final_encoder != "auto":
            print("✅ 一键优化成功应用")
            success = True
        else:
            print("❌ 一键优化未生效")
            success = False
        
        # 清理
        root.destroy()
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 配置保存和读取修复测试")
    print("=" * 50)
    
    tests = [
        ("配置保存和加载", test_config_save_load),
        ("配置文件内容", test_config_file_content),
        ("自动保存回调", test_auto_save_callback),
        ("一键优化功能", test_one_click_optimization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！配置功能正常工作")
        print("\n✨ 修复内容:")
        print("   • 修复了分步编码器变量重复初始化问题")
        print("   • 移除了性能测试按钮，简化界面")
        print("   • 优化了一键优化功能，无需性能测试")
        print("   • 配置能正确保存和加载")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
