#!/usr/bin/env python3
"""
测试FFmpeg静默检测功能
验证修改后的FFmpeg自动检测不再显示弹窗
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ffmpeg_silent_detection():
    """测试FFmpeg静默检测功能"""
    print("🔧 测试FFmpeg静默检测功能...")
    
    try:
        # 导入配置管理器
        from modules.voice_tts.config import config_manager
        
        # 备份当前FFmpeg配置
        original_path = config_manager.get_ffmpeg_path()
        print(f"原始FFmpeg路径: {original_path}")
        
        # 临时清除FFmpeg配置，模拟未配置状态
        config_manager.set_ffmpeg_path("")
        print("已临时清除FFmpeg配置")
        
        # 测试自动检测功能
        print("测试自动检测功能...")
        
        # 导入app模块的检测函数
        from app import AIVoiceApp

        # 创建一个临时的app实例来测试检测功能
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口

        app = AIVoiceApp(root)
        
        # 测试自动检测
        print("运行FFmpeg自动检测...")
        detected_path = app.auto_detect_ffmpeg()
        
        if detected_path:
            print(f"✅ 自动检测成功: {detected_path}")
            
            # 测试检测功能（不应该弹出对话框）
            print("测试配置检查功能...")
            app.check_ffmpeg_configuration()
            print("✅ 配置检查完成，无弹窗显示")
            
        else:
            print("⚠️ 自动检测失败，这是正常的（如果系统中没有FFmpeg）")
            
            # 测试失败情况下的处理（不应该弹出对话框）
            print("测试检测失败时的处理...")
            app.check_ffmpeg_configuration()
            print("✅ 检测失败处理完成，无弹窗显示")
        
        # 恢复原始配置
        config_manager.set_ffmpeg_path(original_path)
        print(f"已恢复原始FFmpeg配置: {original_path}")
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_video_composer_silent():
    """测试视频合成模块的静默处理"""
    print("\n🎬 测试视频合成模块的静默处理...")
    
    try:
        # 导入视频合成模块
        from modules.video_composer.core import VideoComposerProcessor
        
        # 创建处理器实例
        processor = VideoComposerProcessor()
        
        # 测试FFmpeg可用性检查
        is_available = processor.is_available()
        print(f"FFmpeg可用性: {is_available}")
        
        if not is_available:
            print("✅ FFmpeg不可用时，模块正确处理，无弹窗显示")
        else:
            print("✅ FFmpeg可用，模块正常工作")
        
        return True
        
    except Exception as e:
        print(f"❌ 视频合成模块测试失败: {e}")
        return False

def test_configuration_persistence():
    """测试配置持久化"""
    print("\n💾 测试配置持久化...")
    
    try:
        from modules.voice_tts.config import config_manager
        
        # 测试配置读写
        test_path = "/test/ffmpeg/path"
        config_manager.set_ffmpeg_path(test_path)
        
        # 读取配置
        saved_path = config_manager.get_ffmpeg_path()
        
        if saved_path == test_path:
            print("✅ 配置保存和读取正常")
        else:
            print(f"❌ 配置保存失败: 期望 {test_path}, 实际 {saved_path}")
            return False
        
        # 清理测试配置
        config_manager.set_ffmpeg_path("")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔇 FFmpeg静默检测功能测试")
    print("=" * 50)
    
    tests = [
        ("FFmpeg静默检测", test_ffmpeg_silent_detection),
        ("视频合成静默处理", test_video_composer_silent),
        ("配置持久化", test_configuration_persistence),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！FFmpeg静默检测功能正常工作")
        print("\n✨ 修改效果:")
        print("   • FFmpeg自动检测成功时不再显示弹窗")
        print("   • FFmpeg检测失败时不再弹出设置对话框")
        print("   • 视频合成模块FFmpeg不可用时只在日志记录")
        print("   • 用户体验更加流畅，减少不必要的打扰")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
