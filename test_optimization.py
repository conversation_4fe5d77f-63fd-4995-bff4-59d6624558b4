#!/usr/bin/env python3
"""
FFmpeg视频合成优化测试脚本
用于验证优化效果和性能提升
"""

import os
import sys
import time
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from modules.video_composer.ffmpeg_composer import FFmpegVideoComposer
    from modules.video_composer.core import VideoComposerProcessor
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

def test_encoder_optimization():
    """测试编码器优化效果"""
    print("🔧 测试编码器优化...")
    
    try:
        composer = FFmpegVideoComposer()
        
        # 测试速度优先模式
        speed_settings = {
            'speed_priority': True,
            'use_hardware_acceleration': True,
            'enable_parallel': True,
            'max_threads': 8
        }
        
        # 测试平衡模式
        balanced_settings = {
            'speed_priority': False,
            'use_hardware_acceleration': True,
            'enable_parallel': True,
            'max_threads': 8
        }
        
        print("✅ 速度优先模式编码器设置:")
        codec, params = composer._get_optimal_encoder_settings(speed_settings)
        print(f"   编码器: {codec}")
        print(f"   参数: {' '.join(params)}")
        
        print("\n⚖️ 平衡模式编码器设置:")
        codec, params = composer._get_optimal_encoder_settings(balanced_settings)
        print(f"   编码器: {codec}")
        print(f"   参数: {' '.join(params)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 编码器测试失败: {e}")
        return False

def test_hardware_detection():
    """测试硬件检测功能"""
    print("\n🔍 测试硬件检测...")
    
    try:
        composer = FFmpegVideoComposer()

        # 检测硬件编码器
        hardware_encoders = composer._detect_hardware_encoders()
        
        if hardware_encoders:
            print("✅ 检测到硬件编码器:")
            for encoder in hardware_encoders:
                print(f"   • {encoder}")
        else:
            print("⚠️ 未检测到硬件编码器，将使用软件编码")
        
        return True
        
    except Exception as e:
        print(f"❌ 硬件检测失败: {e}")
        return False

def test_performance_benchmark():
    """测试性能基准测试功能"""
    print("\n📊 测试性能基准测试...")
    
    try:
        composer = FFmpegVideoComposer()

        print("正在运行性能基准测试...")
        start_time = time.time()
        
        results = composer.run_performance_benchmark(
            progress_callback=lambda msg: print(f"   {msg}")
        )
        
        end_time = time.time()
        test_duration = end_time - start_time
        
        if results:
            print(f"✅ 性能测试完成 (耗时: {test_duration:.2f}秒)")
            
            if results.get('fastest_encoder'):
                fastest = results['fastest_encoder']
                print(f"🏆 最快编码器: {fastest['name']}")
                print(f"   性能: {fastest['fps']:.1f} FPS")
            
            if results.get('optimization_tips'):
                print("💡 优化建议:")
                for tip in results['optimization_tips']:
                    print(f"   • {tip}")
        else:
            print("❌ 性能测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def test_smart_compression():
    """测试智能压缩功能"""
    print("\n🧠 测试智能压缩...")
    
    try:
        composer = FFmpegVideoComposer()

        # 测试不同设置下的压缩参数
        speed_settings = {'speed_priority': True}
        balanced_settings = {'speed_priority': False}
        
        speed_crf, speed_preset = composer._get_smart_compression_settings(speed_settings)
        balanced_crf, balanced_preset = composer._get_smart_compression_settings(balanced_settings)
        
        print(f"✅ 速度优先压缩: CRF={speed_crf}, preset={speed_preset}")
        print(f"⚖️ 平衡模式压缩: CRF={balanced_crf}, preset={balanced_preset}")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能压缩测试失败: {e}")
        return False

def test_configuration():
    """测试配置文件"""
    print("\n⚙️ 测试配置文件...")
    
    try:
        config_file = "config/video_composer_config.json"
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查新增的配置项
            if 'speed_priority' in config:
                print(f"✅ 速度优先模式: {config['speed_priority']}")
            else:
                print("⚠️ 配置文件中缺少速度优先设置")
            
            print(f"✅ 硬件加速: {config.get('use_hardware_acceleration', False)}")
            print(f"✅ 并行处理: {config.get('enable_parallel', False)}")
            print(f"✅ 最大线程数: {config.get('max_threads', 4)}")
            
        else:
            print("⚠️ 配置文件不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 FFmpeg视频合成优化测试")
    print("=" * 50)
    
    tests = [
        ("编码器优化", test_encoder_optimization),
        ("硬件检测", test_hardware_detection),
        ("智能压缩", test_smart_compression),
        ("配置文件", test_configuration),
        ("性能基准测试", test_performance_benchmark),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！优化功能正常工作")
        print("\n💡 建议:")
        print("   1. 启用速度优先模式获得最佳性能")
        print("   2. 运行性能优化获取个性化建议")
        print("   3. 根据硬件配置调整线程数")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
